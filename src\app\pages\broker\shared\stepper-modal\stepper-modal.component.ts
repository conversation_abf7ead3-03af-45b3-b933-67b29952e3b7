import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { createrequestservice } from '../../services/createrequest.service';
import { PropertyService } from '../../services/property.service';
import Swal from 'sweetalert2';
import { HotelUnitRental } from 'src/app/models/createRequest.model ';

@Component({
  selector: 'app-stepper-modal',
  templateUrl: './stepper-modal.component.html',
  styleUrl: './stepper-modal.component.scss',
})
export class StepperModalComponent implements OnInit {
  totalSteps = 4;
  currentStep = 1;
  brokerId: number;

  cities: any[] = [];
  areas: any[] = [];
  selectedCityId: number;
  selectedCityName: string;
  selectedAreaName: string;
  isLoadingCities = false;

  unitTypes: { key: string; value: string }[] = [];

  specializationScope: { key: string; value: string }[] = [
    {
      key: 'Purchase/Sell Outside Compound',
      value: 'purchase_sell_outside_compound',
    },
    {
      key: 'Purchase/Sell Inside Compound',
      value: 'purchase_sell_inside_compound',
    },
    { key: 'Primary Inside Compound', value: 'primary_inside_compound' },
    { key: 'Resale Inside Compound', value: 'resale_inside_compound' },
    { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },
    { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' },
  ];

  requestTypes: { key: string; value: string }[] = [
    { key: 'Sell', value: 'sell' },
    { key: 'Purchasing', value: 'purchasing' },
    { key: 'Rent Out', value: 'rent_out' },
    { key: 'Rent In', value: 'rent_in' },
  ];

  unitTypeOptions: { key: string; value: string }[] = [
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Basement', value: 'basement' },
    { key: 'Roofs', value: 'roofs' },
    { key: 'Villas', value: 'villas' },
    { key: 'I Villa', value: 'i_villa' },
    { key: 'Twin Houses', value: 'twin_houses' },
    { key: 'Town Houses', value: 'town_houses' },
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Pharmacies', value: 'pharmacies' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factory Lands', value: 'factory_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Standalone Villas', value: 'standalone_villas' },
    {
      key: 'Commercial Administrative Buildings',
      value: 'commercial_administrative_buildings',
    },
    {
      key: 'Commercial Administrative Lands',
      value: 'commercial_administrative_lands',
    },
    { key: 'Residential Buildings', value: 'residential_buildings' },
    { key: 'Residential Lands', value: 'residential_lands' },
    { key: 'Chalets', value: 'chalets' },
    { key: 'Hotels', value: 'hotels' },
    { key: 'Factories', value: 'factories' },
    { key: 'Basements', value: 'basements' },
    { key: 'Full Buildings', value: 'full_buildings' },
    { key: 'Commercial Units', value: 'commercial_units' },
    { key: 'Shops', value: 'shops' },
    { key: 'Mixed Housings', value: 'mixed_housings' },
    { key: 'Cooperatives', value: 'cooperatives' },
    { key: 'Youth Units', value: 'youth_units' },
    { key: 'Ganat Misr', value: 'ganat_misr' },
    { key: 'Dar Misr', value: 'dar_misr' },
    { key: 'Sakan Misr', value: 'sakan_misr' },
    { key: 'Industrial Lands', value: 'industrial_lands' },
    { key: 'Cabin', value: 'cabin' },
    { key: 'Chalet', value: 'chalet' },
    { key: 'Vacation Villa', value: 'vacation_villa' },
    { key: 'Hotel Units', value: 'hotel_units' },
  ];

  floorTypes: { key: string; value: string }[] = [
    { key: 'Ground Floor', value: 'ground' },
    { key: 'First Floor', value: 'first' },
    { key: 'Second Floor', value: 'second' },
    { key: 'Basement', value: 'basement' },
    { key: 'Top Floor', value: 'top' },
  ];

  furnishingStatusTypes: { key: string; value: string }[] = [
    { key: 'Furnished', value: 'furnished' },
    { key: 'Semi-Furnished', value: 'semi_furnished' },
    { key: 'Unfurnished', value: 'unfurnished' },
  ];

  deliveryStatusTypes: { key: string; value: string }[] = [
    { key: 'Immediate Delivery', value: 'immediate_delivery' },
    { key: 'Under Construction', value: 'under_construction' },
  ];

  financialStatusTypes: { key: string; value: string }[] = [
    { key: 'Paid in Full', value: 'paid_in_full' },
    {
      key: 'Partially Paid with Remaining Installments',
      value: 'partially_paid_with_remaining_installments',
    },
  ];

  legalStatusTypes: { key: string; value: string }[] = [
    { key: 'Licensed', value: 'licensed' },
    { key: 'Reconciled', value: 'reconciled' },
    { key: 'Reconciliation Required', value: 'reconciliation_required' },
  ];

  fitOutConditionTypes: { key: string; value: string }[] = [
    { key: 'Unfitted', value: 'unfitted' },
    { key: 'Fully Fitted', value: 'fully_fitted' },
    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
  ];

  finishingStatusTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  unitViewTypes: { key: string; value: string }[] = [
    { key: 'Garden', value: 'garden' },
    { key: 'Street', value: 'street' },
    { key: 'Sea', value: 'sea' },
    { key: 'Pool', value: 'pool' },
  ];

  unitPositionTypes: { key: string; value: string }[] = [
    { key: 'Right Side', value: 'right' },
    { key: 'Left Side', value: 'left' },
  ];

  amenitiesTypes: { key: string; value: string }[] = [
    { key: 'Parking', value: 'hasParking' },
    { key: 'Swimming Pool', value: 'hasSwimmingPool' },
    { key: 'Elevator', value: 'hasElevator' },
    { key: 'Club House', value: 'hasClubHouse' },
  ];

  otherAccessoriesTypes: { key: string; value: string }[] = [
    { key: 'Air Conditioning', value: 'air_conditioning' },
    { key: 'Balcony', value: 'balcony' },
    { key: 'Garden', value: 'garden' },
    { key: 'Security System', value: 'security_system' },
    { key: 'Internet', value: 'internet' },
  ];

  // Form groups for each step
  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private createRequestService: createrequestservice,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.brokerId = 5;
    this.initForms();
    this.loadUnitTypes();
    this.loadCities();
    this.loadAreas();
  }

  initForms() {
    // Step 1: Basic Request Settings
    this.step1Form = this.fb.group({
      locationType: ['', Validators.required],
      requestType: ['', Validators.required],
      unitType: ['', Validators.required],
    });

    // Step 2: Location Information
    this.step2Form = this.fb.group({
      compoundName: [''],
      mallName: [''],
      cityId: ['', Validators.required],
      areaId: ['', Validators.required],
      detailedAddress: [''],
      location: [''],
      // location: [
      //   '',
      //   [Validators.required, Validators.pattern(/^https?:\/\/.+/)],
      // ],
      locationSuggestion: [false],
    });

    // Step 3: Unit Information
    this.step3Form = this.fb.group({
      unitNumber: ['', Validators.required],
      propertyNumber: ['', Validators.required],
      unitType: ['', Validators.required],
      unitArea: [null, [Validators.required, Validators.min(1)]],
      unitAreaMin: [null, Validators.min(0)],
      unitAreaMax: [null, Validators.min(0)],
      buildingArea: [null, Validators.min(0)],
      buildingAreaMin: [null, Validators.min(0)],
      buildingAreaMax: [null, Validators.min(0)],
      groundArea: [null, Validators.min(0)],
      groundAreaMin: [null, Validators.min(0)],
      groundAreaMax: [null, Validators.min(0)],
      gardenArea: [null, Validators.min(0)],
      terraceArea: [null, Validators.min(0)],
      roomsCount: [null, [Validators.required, Validators.min(0)]],
      bathroomsCount: [null, [Validators.required, Validators.min(0)]],
      floorsCount: [null, [Validators.required, Validators.min(0)]],
      floor: ['', Validators.required],
      furnishingStatus: ['', Validators.required],
      deliveryStatus: ['', Validators.required],
      financialStatus: ['', Validators.required],
      legalStatus: ['', Validators.required],
      fitOutCondition: ['', Validators.required],
      finishingStatus: ['', Validators.required],
      unitView: [''],
      unitPosition: [''],
      hasParking: [false],
      hasSwimmingPool: [false],
      hasElevator: [false],
      hasClubHouse: [false],
    });

    // Step 4: Financial Information
    this.step4Form = this.fb.group({
      monthlyRentFrom: [null, Validators.min(0)],
      monthlyRentTo: [null, Validators.min(0)],
      wantRealEstateAgentOffers: [false],
      dailyRentFrom: [null, Validators.min(0)],
      dailyRentTo: [null, Validators.min(0)],
    });
  }

  // Get current form based on step
  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      default:
        return this.step1Form;
    }
  }

  // Load unit types from backend
  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Unit Types loaded:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step2Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;
    this.step2Form.patchValue({
      areaId: areaId,
    });
  }

  getText(array: { key: string; value: string }[], value: string): string {
    const item = array.find((item) => item.value === value);
    return item ? item.key : '';
  }

  select(form: FormGroup, field: string, value: string) {
    form.patchValue({ [field]: value });
  }

  getSelectedAmenitiesText(): string {
    const selectedAmenities = this.amenitiesTypes.filter(
      (amenity) => this.step3Form.get(amenity.value)?.value
    );

    if (selectedAmenities.length === 0) {
      return '';
    } else if (selectedAmenities.length === 1) {
      return selectedAmenities[0].key;
    } else {
      return `${selectedAmenities.length} amenities selected`;
    }
  }

  // Check if amenity is selected
  isAmenitySelected(amenityValue: string): boolean {
    return this.step3Form.get(amenityValue)?.value || false;
  }

  // Toggle individual amenity
  toggleAmenity(amenityValue: string) {
    const currentValue = this.step3Form.get(amenityValue)?.value;
    this.step3Form.patchValue({ [amenityValue]: !currentValue });
  }

  // Handle "All amenities" selection
  onAllAmenitiesChange(event: any) {
    const isChecked = event.target.checked;
    const amenityValues: { [key: string]: boolean } = {};

    this.amenitiesTypes.forEach((amenity) => {
      amenityValues[amenity.value] = isChecked;
    });

    this.step3Form.patchValue(amenityValues);
  }

  // Check if all amenities are selected
  areAllAmenitiesSelected(): boolean {
    return this.amenitiesTypes.every(
      (amenity) => this.step3Form.get(amenity.value)?.value
    );
  }

  // Other Accessories methods
  getSelectedAccessoriesText(): string {
    const selectedAccessories = this.otherAccessoriesTypes.filter((accessory) =>
      this.isAccessorySelected(accessory.value)
    );

    if (selectedAccessories.length === 0) {
      return '';
    } else if (selectedAccessories.length === 1) {
      return selectedAccessories[0].key;
    } else {
      return `${selectedAccessories.length} accessories selected`;
    }
  }

  isAccessorySelected(accessoryValue: string): boolean {
    const otherAccessories =
      this.step4Form.get('otherAccessories')?.value || [];
    return otherAccessories.includes(accessoryValue);
  }

  toggleAccessory(accessoryValue: string) {
    const currentAccessories =
      this.step4Form.get('otherAccessories')?.value || [];
    let updatedAccessories;

    if (currentAccessories.includes(accessoryValue)) {
      updatedAccessories = currentAccessories.filter(
        (item: string) => item !== accessoryValue
      );
    } else {
      updatedAccessories = [...currentAccessories, accessoryValue];
    }

    this.step4Form.patchValue({ otherAccessories: updatedAccessories });
  }

  onAllAccessoriesChange(event: any) {
    const isChecked = event.target.checked;

    if (isChecked) {
      const allAccessories = this.otherAccessoriesTypes.map(
        (accessory) => accessory.value
      );
      this.step4Form.patchValue({ otherAccessories: allAccessories });
    } else {
      this.step4Form.patchValue({ otherAccessories: [] });
    }
  }

  // Submit the form
  submitForm() {
    if (this.isCurrentFormValid()) {
      const formData: HotelUnitRental = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value,
      };

      console.log('Form submitted with data:', formData);

      // Create FormData for backend submission
      const httpFormData = new FormData();

      // Add step1 form data
      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      // Add step2 form data
      Object.keys(this.step2Form.value).forEach((key) => {
        httpFormData.append(key, this.step2Form.value[key]);
      });

      // Add step3 form data
      Object.keys(this.step3Form.value).forEach((key) => {
        httpFormData.append(key, this.step3Form.value[key]);
      });

      // Add step4 form data
      Object.keys(this.step4Form.value).forEach((key) => {
        httpFormData.append(key, this.step4Form.value[key]);
      });

      // Add broker ID
      httpFormData.append('brokerId', this.brokerId.toString());

      // Show loading state
      const button = document.querySelector('.btn-primary');
      if (button) {
        button.classList.add('btn-loading');
      }

      // Submit to backend
      this.createRequestService.createRequest(httpFormData).subscribe({
        next: async (response) => {
          console.log('Request submitted successfully:', response);
          await Swal.fire('Request submitted successfully!', '', 'success');

          // this.router.navigate(['/broker/requests']);
        },
        error: (err) => {
          console.error('Error submitting request:', err);
          Swal.fire(
            'Error submitting request',
            err.message || 'Please try again',
            'error'
          );

          // Remove loading state
          if (button) {
            button.classList.remove('btn-loading');
          }
        },
        complete: () => {
          this.cdr.detectChanges();
          // Remove loading state
          if (button) {
            button.classList.remove('btn-loading');
          }
        },
      });
    }
  }

  // Check if current form is valid
  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  // Navigate to next step
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  // Navigate to previous step
  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }
}
